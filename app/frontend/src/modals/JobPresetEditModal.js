import  React, { useState,  useEffect } from 'react';
import {<PERSON><PERSON>, Button, Form} from 'react-bootstrap';
import { validate_Date } from '../pages/utils';
import { v4 as uuidv4 } from 'uuid';

/**

@param {Object} args.presetData - The component props.
@param {Function} args.handleSavePreset - The function to be called when the modal is closed.
@returns {JSX.Element} The JobPresetModal component.

**/


const JobPresetEditForm = (args) =>{

    const [data, setData] = useState(args.isPowerAdmin && args.PendingApprovals==false ? args.presetData: args.UNData || "");
    const [JobTitle, SetJobTitle] = useState(data?.Job_Title || "");
    const [JobDescription, SetJobDescription] = useState(data?.Job_Description || "");
    const [Client_Manager, setClientManager] = useState(data?.Client_Manager || "")
    const [Client_Name, setClientName] = useState(data?.Client_Name || "")
    const [Created_By, SetCreatedBy] = useState(data?.creator || "")
    const [MandatoryQS, setMandatoryQS] = useState(data?.MandatoryQS || [])
    const [Uuid, setUUid] = useState(data?.JD_Uuid || "")
    const [openModal, setOpenModal] = useState(false)
   

    useEffect(() =>{
        if(args.presetData.length !=0 || args.UNData.length !=0 && args.isOpen==true)
        {
            setData(args.isPowerAdmin && args.PendingApprovals==false ? args.presetData: args.UNData || "")
        
            if(args.isOpen==true)
            {  
                setOpenModal(true)
                SetJobTitle(data?.Job_Title)
                SetJobDescription(data?.Job_Description)
                setClientManager(data?.Client_Manager)
                setClientName(data?.Client_Name)
                SetCreatedBy(data?.creator)
                setUUid(data?.JD_Uuid)
                setMandatoryQS(data?.MandatoryQS || [])
                console.log("Data TEST: ", data?.MandatoryQS)
            }
        } 
        
    } , [data, args.presetData, args.UNData])



    function handleInputArray(index, field, value) {
        setMandatoryQS(prevState => {
            const newState = [...prevState];
                newState[index] = newState[index] || { question: "", lookFor: "" };
    
            newState[index] = {
                ...newState[index], 
                [field]: value
            };
            console.log(newState)
            return newState;
        });
    }

    async function handleSave (uuid)  {
    
        if ((JobDescription && JobTitle && Client_Manager && Client_Name && Created_By && MandatoryQS) != '')
        {
            const date = await validate_Date()
            if(date){
             let saveData = {
                 JD_Uuid : uuid,
                 Job_Title: JobTitle,
                 Job_Description : JobDescription,
                 date : date,
                 creator: Created_By,
                 Client_Name: Client_Name,
                 Client_Manager: Client_Manager,
                 Status: data.Status,
                 MandatoryQS: MandatoryQS
 
             };
 
        args.handleSavePreset(saveData)
     
        args.setSelectedPreset([])
        await args.loadPresetsDB()
        
            onClose()
         }   
        }
    }
    
    
    const onClose = async () =>{
       
        args.setSelectedPreset([])
        args.loadPresetsDB()
        args.setPresetOpen(false)
        setOpenModal(false)
    }

    return (
        <>
        
        <Modal style={{"--bs-modal-width": "1000px"}} show = {openModal} id='editPresetModal' tabIndex={-1}>
            <Modal.Header closeButton onClick={onClose}>
                <Modal.Title>Edit Preset</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <Form>
                    <Form.Group className='mb-3'>
                    <Form.Label>Job Title</Form.Label>
                    <Form.Control autoFocus value ={JobTitle} onChange={(e) =>SetJobTitle(e.target.value)}>
                    </Form.Control>
                    </Form.Group>


                    <Form.Group className='mb-3'>
                    <Form.Label>Job Description</Form.Label>
                    <Form.Control as ="textarea" rows={3} value={JobDescription} onChange={(e) => SetJobDescription(e.target.value)}></Form.Control>
                    </Form.Group>


                    <Form.Group className='mb-3'>
                    <Form.Label>Client Name</Form.Label>
                    <Form.Control as ="textarea" rows={3}  value={Client_Name} onChange={(e) => setClientName(e.target.value)}></Form.Control>
                    </Form.Group>

                    <Form.Group className='mb-3'>
                    <Form.Label>Client Manager</Form.Label>
                    <Form.Control as ="textarea" rows={3} value={Client_Manager} onChange={(e) => setClientManager(e.target.value)}></Form.Control>
                    </Form.Group>

                    <Form.Group className='mb-3'>
                    <Form.Label>Created By</Form.Label>
                    <Form.Control as ="textarea" rows={3}  value={Created_By} onChange={(e) => SetCreatedBy(e.target.value)}></Form.Control>
                    </Form.Group>
                   
                    <Form.Group className='mb-3'>
                    <div className='separator mt-3 mb-5'></div>
                    <h3 style={{marginTop:'1%'}}>Mandatory Questions</h3>
                    
                    {MandatoryQS?.map((entry, index) => 
                        <>
                        <Form.Label>Question {index + 1}</Form.Label>
                        <Form.Control key = {index} as ="textarea" rows={3} value={entry?.question}  style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(index, "question", e.target.value)}></Form.Control>
                        <Form.Label>Look For:</Form.Label>
                        <Form.Control key = {index} as ="textarea" rows={3} value={entry?.lookFor} style={{marginBottom:'3%'}} onChange={(e) => handleInputArray(index, "lookFor", e.target.value)}></Form.Control>
                        <div className='separator mt-3 mb-5'></div>
                        </>
                    )}
                    </Form.Group>

                    <p>Status: {data?.Status}</p>
                </Form>
            </Modal.Body>
            <Modal.Footer>
                <Button variant ='secondary' onClick={() => onClose()}>Close</Button>
                <Button style={{backgroundColor:'orange'}} onClick={() => handleSave(uuidv4())}>Clone</Button>
                <Button style={{backgroundColor:'green'}} variant = 'primary' onClick={() => handleSave(Uuid)}>Save Changes</Button>
               

            </Modal.Footer>

        </Modal>

    </>
    );
}


    
export default JobPresetEditForm