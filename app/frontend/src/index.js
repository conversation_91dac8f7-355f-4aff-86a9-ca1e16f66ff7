import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';
import { PublicClientApplication, EventType } from "@azure/msal-browser";
import { msalConfig } from "./auth-config.js";
import './App.css';
import App from './App';
import 'bootstrap/dist/css/bootstrap.min.css';
import 'bootstrap/dist/js/bootstrap.bundle.min.js';

/**
* MSAL should be instantiated outside of the component tree to prevent it from being re-instantiated on re-renders.
* For more, visit: https://github.com/AzureAD/microsoft-authentication-library-for-js/blob/dev/lib/msal-react/docs/getting-started.md
*/
const msalInstance = new PublicClientApplication(msalConfig);
var UserRole = "user"
var UserName =""
var UserImgURL = ""
function checkUserRoles(roles) {
 
  const requiredRole = "admin"; 
  if (roles && roles.includes(requiredRole)) {
     
      // User has the required role, allow access
      console.log("User has admin access");
      UserRole = (requiredRole)
    
  } 
  else {
      // User doesn't have the required role, restrict access
      console.log("User has user access");
      UserRole = "user"
      
  }

}

export function getUserRole(){
  return UserRole
}

export function getUserImage(){ // returns the img in a URL
  return UserImgURL
}

// Default to using the first account if no account is active on page load
if (!msalInstance.getActiveAccount() && msalInstance.getAllAccounts().length > 0) {
    // Account selection logic is app dependent. Adjust as needed for different use cases.
    msalInstance.setActiveAccount(msalInstance.getAllAccounts()[0]);
}

// Register a callback function that will be executed after one of the success conditions below is met and 
// event.payload.account exists. Then, active account(which was just authenticated) is set in MSAL instance
msalInstance.addEventCallback( (event) => {
    if (
        (event.eventType === EventType.LOGIN_SUCCESS ||
            event.eventType === EventType.ACQUIRE_TOKEN_SUCCESS ||
            event.eventType === EventType.SSO_SILENT_SUCCESS) &&
        event.payload.account
    ) {
        const accessToken = event.payload.accessToken
        UserImgURL = MSAL_User_Image(accessToken)
        msalInstance.setActiveAccount(event.payload.account);
        const account = event.payload.account;
        UserName = account.name
        const idTokenClaims = account.idTokenClaims;
        const roles = idTokenClaims.roles || idTokenClaims['http://schemas.microsoft.com/ws/2008/06/identity/claims/role'];
        checkUserRoles(roles[0])
    }
});

export function getUserName(){
  return UserName
}

function MSAL_User_Image(token)
{
  const graphApiUrl = 'https://graph.microsoft.com/v1.0/me/photo/$value';
  const response = fetch(graphApiUrl, {
    method: 'GET',
    headers: {
        'Authorization': `Bearer ${token}` 
    }
});
  if (response.ok) {
    const imageBlob = response.blob();
    const imageUrl = URL.createObjectURL(imageBlob);
    console.log('Profile picture URL:', imageUrl);
    return imageUrl;

} else {
  return 'https://cdn-icons-png.flaticon.com/512/4519/4519729.png'
}

}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <div className="App">
      <App instance={msalInstance}></App>
    </div>
  </React.StrictMode>
);
