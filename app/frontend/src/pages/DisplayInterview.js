import '../App.css';
import TextareaAutosize from 'react-textarea-autosize';
import React, { useEffect, useState } from 'react';

import { Navbar, Container, Image, Form, FormControl, DropdownToggle, DropdownMenu} from 'react-bootstrap';
import CreospanLogo from '../assets/creospanLogo.png';
import NotificationIcon from '../assets/NotificationIcon.png';
import Profile from '../assets/Ravi.jpeg'
import { Sidebar, Menu, MenuItem, SubMenu } from 'react-pro-sidebar';

import {getAndDownloadInfo} from './utils.js'

// Side Bar Icons
import { DashboardIcon } from '../assets/icons/dashboard.tsx';
import { GenerateIcon } from '../assets/icons/generate.tsx';
import { PastInterviewIcon } from '../assets/icons/pastinterview.tsx';
import { JobDescriptionIcon} from '../assets/icons/jobdescription.tsx';
import { MembersIcon } from '../assets/icons/members.tsx';
import { SettingsIcon} from '../assets/icons/settings.tsx';
import { LockIcon } from '../assets/icons/lockIcon.tsx';
import { BackArrow } from '../assets/icons/backarrow.tsx';
import { ReloadIcon} from '../assets/icons/reload.tsx'
import ApproveModal from '../modals/ApproveModal.js';
const backend_key = process.env.REACT_APP_BACKEND_API;


/**
 * Renders the DisplayInterview component.
 *
 * @param {Object} args - The arguments object.
 * @returns {JSX.Element} The JSX element representing the DisplayInterview component.
 */
const DisplayInterview = (args) => {
   
    const [STRClicked, SetSTRClicked] = useState(true)
    const [CandidateClicked, SetCandidateClicked] = useState(false)
    const [ProfileClicked, SetProfileClicked] = useState(false)
    const [TechnicalClicked, SetTechnicalClicked] = useState(false)
    const [BehavioralClicked, setBehavioralClicked] = useState(false)
    //const [ShowDownload, setDownloadShown] = useState(args.showDownloadButton)
    //const [ImageFound, setImageFound] = useState(args.imageFound)
    const [Mandatory_Questions, setMandatoryQS] = useState(args?.candidateInfo?.Mandatory_Questions ?? [])
    const [MandatoryClicked, setMandatoryClicked] = useState()
    const [interviewState, setInterviewState] = useState(args?.candidateInfo?.Status)
    const [approveOpened, setApproveOpened] = useState(false)
    const [unsavedData, setUnsavedData] = useState(false)
  
   
    useEffect(() => {
        console.log("Modal open state changed: ", approveOpened);
      }, [approveOpened]);
    
    let buttonName = ""
    let isReadOnly = false
    let notesReadOnly = false
    console.log("Mand Questions: ", args?.candidateInfo?.Mandatory_Questions)
    if(interviewState === "Pending") {
        buttonName = "Approve"
        isReadOnly = false
        notesReadOnly = false
        console.log(`Interview State: ${interviewState}, isReadOnly: ${isReadOnly}`)
    }
    else if(interviewState === "Approved") {
        buttonName = "Generate Report"
        isReadOnly = true
        notesReadOnly = false
        console.log(`Interview State: ${interviewState}, isReadOnly: ${isReadOnly}`)

    }   
    else {
        isReadOnly = true
        notesReadOnly = true
        console.log(`Interview State: ${interviewState}, isReadOnly: ${isReadOnly}`)
    }

    const handleStateChange = () => {
        console.log("State Changed")
        setApproveOpened(true)
    }
      
   const regenerateTechnical = (num) => {
    const report = `Assessment: ${args.candidateInfo.assessment}, Skills Lacking: ${args.candidateInfo.lacking_qualities}, Technical Questions: ${args.candidateInfo.technical_questions} ` 
    const fileForm = new FormData()
    fileForm.append('report', String(report))
    fileForm.append('jobDescription', String(args.candidateInfo.Job_Description_Title))
    fileForm.append('questionNumber', String(num))
    fetch('/regenerateTechnical', {
        method: 'POST',
        headers: {
            'my-custom-header-key': backend_key,
        },
        credentials: 'include',
        body: fileForm,
    })
    .then((response) => response.json())
    .then(async (data) => {
        console.log(data[0])
        const answer = data[0][0].answer
        const question = data[0][0].question
        handleNotesChange('technical', 'regenQuestion', num, question)
        handleNotesChange('technical', 'regenAnswer', num, answer)
        console.log("updatedInfo: ", args?.candidateInfo)
        saveCandidateData()
    })
   }

   const regenerateBehavioral = (num) => {
        const report = `Assessment: ${args.candidateInfo.assessment}, Skills Lacking: ${args.candidateInfo.lacking_qualities}, Behavioral Questions: ${args.candidateInfo.general_questions} ` 
        const fileForm = new FormData()
        fileForm.append('report', String(report))
        fileForm.append('jobDescription', String(args.candidateInfo.Job_Description_Title))
        fileForm.append('questionNumber', String(num))

        fetch('/regenerateBehavioral', {
                method: 'POST',
                headers: {
                    'my-custom-header-key': backend_key,
                },
                credentials: 'include',
                body: fileForm,
            })
            .then((response) => response.json())
            .then(async (data) => {
                
                console.log(data[0])
                const answer = data[0][0].answer
                const question = data[0][0].question
                handleNotesChange('general', 'regenQuestion', num, question)
                handleNotesChange('general', 'regenAnswer', num, answer)
                console.log("updatedInfo: ", args?.candidateInfo)
                saveCandidateData()
            })
   }
    
    /**
     * Handles the change in notes for a specific section, type, and number.
     * Updates the candidate information accordingly.
     * 
     * @param {string} section - The section of the candidate information (e.g., 'general', 'technical', 'lackingQualities').
     * @param {string} type - The type of information to be updated (e.g., 'question', 'notes', 'answer').
     * @param {number} num - The index of the question or information to be updated.
     * @param {Event} event - The event object representing the change in notes.
     */
    const handleNotesChange = (section, type, num, event) => {
        let newCandidateInfo = {...args?.candidateInfo};
        switch(section){
            case 'general':
                type === 'question' ? newCandidateInfo.general_questions[num].question = event.target.value : type === "regenQuestion" ? newCandidateInfo.general_questions[num].question = event : type === "regenAnswer" ? newCandidateInfo.general_questions[num].answer = event : type === 'notes' ? newCandidateInfo.general_questions[num].notes = event.target.value : newCandidateInfo.general_questions[num].answer = event.target.value;
                break
            case 'technical':
                type === 'question' ? newCandidateInfo.technical_questions[num].question = event.target.value : type === 'notes' ? newCandidateInfo.technical_questions[num].notes = event.target.value : type === "regenAnswer" ? newCandidateInfo.technical_questions[num].answer = event : type === "regenQuestion" ? newCandidateInfo.technical_questions[num].question = event: newCandidateInfo.technical_questions[num].answer = event.target.value;
                break
            case 'lackingQualities':
                newCandidateInfo.lacking_qualities = event.target.value;
                break
            case 'strengths':
                newCandidateInfo.strengths = event.target.value;
                break
            case 'assessment':
                newCandidateInfo.assessment = event.target.value;
                break
            case 'linkedCompare':
                newCandidateInfo.linkedCompare = event.target.value;
                break
            case 'MandatoryQS':
                type === 'question' ? newCandidateInfo.Mandatory_Questions[num].question = event.target.value : type === 'lookFor' ? newCandidateInfo.Mandatory_Questions[num].lookFor = event.target.value : newCandidateInfo.Mandatory_Questions[num].notes = event.target.value;
                break
            case 'overall':
                newCandidateInfo.overall_notes = event.target.value;
                break
            
            default:
                break
        }

        args.setCandidateInfo(newCandidateInfo);
        setUnsavedData(true)
       
    }

    const saveCandidateData = () => {
        args.handleSaveCandidate(args?.candidateInfo)
        setUnsavedData(false)
    }

    const customQuestion = (type) => {
        let newCandidateInfo = {...args?.candidateInfo};
        if(type === 'behavioral') {
            newCandidateInfo.general_questions.push({question: '', answer: '', notes: ''});
        } 
        else if(type === 'mandatory') {
            newCandidateInfo.Mandatory_Questions.push({question: '', answer: '', lookFor: ''})
        }
        else {
            newCandidateInfo.technical_questions.push({question: '', answer: '', notes: ''});
        }
            args.setCandidateInfo(newCandidateInfo);
    }

    // saves the candidate information and returns to the home page
    const goBack = () => {
      //  args.handleSaveCandidate({...args?.candidateInfo})
        Dashboard_Page()
      
    }

    const generateInterviewPage =() =>{
        args.setViewDash(true)
        args.setNewInterview(true)
        args.setDownloadShown(false)
        args.setJobDescPage(false)
    }

    const pastInterviewsPage =() =>{
        args.setNewInterview(false)
        args.loadDB((!args.viewDB), (!args.viewDash))
        args.setDownloadShown(false)
        args.setJobDescPage(false)

    }
    const Dashboard_Page = () =>{
        args.setNewInterview(false)
        args.setViewDB(false)
        args.setViewDash(true)
        args.setDownloadShown(false)
        args.setJobDescPage(false)
    }
    const JobDescFilePage =() =>{
        args.setNewInterview(false)
        args.setViewDB(false)
        args.setViewDash(true)
        args.setDownloadShown(false)
        args.setJobDescPage(true)
    }
    
    const handleSTRClicked = () => {
        setMandatoryClicked(false)
        SetSTRClicked(true)
        SetCandidateClicked(false)
        SetProfileClicked(false)
        SetTechnicalClicked(false)
        setBehavioralClicked(false)
        const strID = document.getElementById("str")
        const scoreID = document.getElementById("score")
        const profileID = document.getElementById("profile")
        const techID = document.getElementById("tech")
        const behavioralID = document.getElementById("behavioral")
        const mandID = document.getElementById("mandatory")
       
        strID.style.color = "orange"
        scoreID.style.color = "#887e7e"
        profileID.style.color = "#887e7e"
        techID.style.color = "#887e7e"
        behavioralID.style.color = "#887e7e"
        if(mandID) mandID.style.color = "#887e7e"
    }
    const handleCandClicked = () =>{
        SetCandidateClicked(true)
        SetProfileClicked(false)
        SetTechnicalClicked(false)
        setBehavioralClicked(false)
        SetSTRClicked(false)
        setMandatoryClicked(false)
        const strID = document.getElementById("str")
        const scoreID = document.getElementById("score")
        const profileID = document.getElementById("profile")
        const techID = document.getElementById("tech")
        const behavioralID = document.getElementById("behavioral")
        const mandID = document.getElementById("mandatory")
        if (mandID) mandID.style.color = "#887e7e"
        strID.style.color = "#887e7e"
        scoreID.style.color = "orange"
        profileID.style.color = "#887e7e"
        techID.style.color = "#887e7e"
        behavioralID.style.color = "#887e7e"
    
    }

    const handleMandatoryClicked = () =>{
        SetCandidateClicked(false)
        SetProfileClicked(false)
        SetTechnicalClicked(false)
        setBehavioralClicked(false)
        SetSTRClicked(false)
        setMandatoryClicked(true)
        const strID = document.getElementById("str")
        const scoreID = document.getElementById("score")
        const profileID = document.getElementById("profile")
        const techID = document.getElementById("tech")
        const behavioralID = document.getElementById("behavioral")
        const mandID = document.getElementById("mandatory")

        strID.style.color = "#887e7e"
        scoreID.style.color = "#887e7e"
        profileID.style.color = "#887e7e"
        techID.style.color = "#887e7e"
        behavioralID.style.color = "#887e7e"
        if(mandID)  mandID.style.color = "orange"
    }

    const handleProfileClicked = () =>{
        SetCandidateClicked(false)
        SetProfileClicked(true)
        SetTechnicalClicked(false)
        setBehavioralClicked(false)
        SetSTRClicked(false)        
        setMandatoryClicked(false)
        const strID = document.getElementById("str")
        const scoreID = document.getElementById("score")
        const profileID = document.getElementById("profile")
        const techID = document.getElementById("tech")
        const behavioralID = document.getElementById("behavioral")
        const mandID = document.getElementById("mandatory")

        if(mandID)  mandID.style.color = "#887e7e"
        strID.style.color = "#887e7e"
        scoreID.style.color = "#887e7e"
        profileID.style.color = "orange"
        techID.style.color = "#887e7e"
        behavioralID.style.color = "#887e7e"
    }
    const handleTechClicked = () =>{
        SetCandidateClicked(false)
        SetProfileClicked(false)
        SetTechnicalClicked(true)
        setBehavioralClicked(false)
        SetSTRClicked(false)
        setMandatoryClicked(false)

        const strID = document.getElementById("str")
        const scoreID = document.getElementById("score")
        const profileID = document.getElementById("profile")
        const techID = document.getElementById("tech")
        const behavioralID = document.getElementById("behavioral")
        const mandID = document.getElementById("mandatory")
        if(mandID)  mandID.style.color = "#887e7e"
        strID.style.color = "#887e7e"
        scoreID.style.color = "#887e7e"
        profileID.style.color = "#887e7e"
        techID.style.color = "orange"
        behavioralID.style.color = "#887e7e"
    }
    const handleBehavioralClicked = () =>{
        SetCandidateClicked(false)
        SetProfileClicked(false)
        SetTechnicalClicked(false)
        setBehavioralClicked(true)
        SetSTRClicked(false)
        setMandatoryClicked(false)

        const strID = document.getElementById("str")
        const scoreID = document.getElementById("score")
        const profileID = document.getElementById("profile")
        const techID = document.getElementById("tech")
        const behavioralID = document.getElementById("behavioral")
        const mandID = document.getElementById("mandatory")
        if(mandID)  mandID.style.color = "#887e7e"
        
        strID.style.color = "#887e7e"
        scoreID.style.color = "#887e7e"
        profileID.style.color = "#887e7e"
        techID.style.color = "#887e7e"
        behavioralID.style.color = "orange"
    }
    
    

    const handleApprovalClose = async () => {
        await args.loadDB((!args.viewDB), (args.viewDash))
        pastInterviewsPage()
    }

    // Temporary solution to remove Education Level and Communication Skills from candidate Score. Not to be used as a long term solution
    // TODO: Once backend database can be backed-up. Alter the backend prompt that generates score to remove these fields.
    const tempFixScore = (scoreStr) => {
        let scoreArray = scoreStr.split('\n\n');
        let filteredArr = scoreArray.filter((str) => !str.includes('Education Level') && !str.includes('Communication Skills'));
        let returnStr = filteredArr.join('\n\n');
        return returnStr
    }

    return (
        <>
             <Navbar className="navbar justify-content-between" style={{"height": "60px", padding:"0px"}}>  
               <Navbar.Brand href="#" style={{display:'flex', alignItems:'center'}} > 
               <Image src={CreospanLogo} roundedCircle width="60" height="60" />
               </Navbar.Brand>
              <Container className='d-flex flex-row-reverse'>
               <Form className='d-flex flex-row-reverse' style={{borderRadius:"50px"}}>
                  <FormControl type='search' placeholder='🔍 Search' style={{display:'flex', alignItems:'center', backgroundColor:'#e49d60', border:'#e08537', paddingRight:"210px", color:'white'}}></FormControl>
               </Form>
               </Container>
           <Navbar.Brand href='#'>
               <Image src={NotificationIcon} roundedCircle width="30" height="30"></Image>
           </Navbar.Brand>
           <Navbar.Brand href ='#'>
               <Image src={Profile} roundedCircle width="40" height="40"></Image>
           </Navbar.Brand>
        </Navbar>

        <div className='row main' style={{"marginLeft":'0px',"flex":"1","--bs-gutter-x":"0", backgroundColor:'#f5f5f5'}}>
        <Sidebar style={{"width":"5%", "min-width": '5%','color':'orange', 'overflow':'auto', backgroundColor:'white !important'}}>
            <Menu iconShape="square" style={{"margin-top":"40%", backgroundColor:'white', borderColor: 'white', width:'100%'}} >
            <MenuItem onClick={Dashboard_Page} icon={<DashboardIcon color='#E08537'/>} style={{marginLeft:'0%'}}><div style={{marginTop:'5%'}}> </div> </MenuItem>
            <MenuItem onClick={generateInterviewPage} icon={<GenerateIcon color='#E08537'/>} style={{marginLeft:'3%', marginTop:'1%'}}>  </MenuItem>
            <MenuItem onClick={pastInterviewsPage} icon={<PastInterviewIcon color='#E08537'/>} style={{marginLeft:'3%'}}>  </MenuItem>
            <MenuItem onClick={JobDescFilePage} icon={<JobDescriptionIcon color='#E08537'/>} style={{marginLeft:'3%', marginTop:'2%'}}>  </MenuItem>
            <MenuItem icon={<MembersIcon color='#E08537'/>} style={{marginLeft:'3%', backgroundColor:'grey'}}>  </MenuItem>
            <MenuItem icon={<SettingsIcon color='#E08537'/>} style={{marginLeft:'3%', backgroundColor:'grey'}}> </MenuItem>
            </Menu>
            </Sidebar>
            <button className='fixed-bottom ' style={{"margin": "80px 0 0 20px", "width":"80px", border:'none', backgroundColor:'white'}} onClick={goBack}><BackArrow></BackArrow></button>
            <div className='convo-section mx-auto' style={{overflow: "initial", flex:1}}>
                <div style={{backgroundColor:'#f5f5f5'}}>
                <div className='w-100 file-upload-info d-flex justify-content-center' style={{backgroundColor: 'black', height:'50px', marginBottom:'0px !important', paddingLeft:'3%'}}> 
                        <p className='m-3 p-0'>
                        {args?.candidateInfo?.Candidate_Name === "NOT FOUND" ? args?.candidateInfo?.Nick_Name:  args?.candidateInfo?.Candidate_Name + " / " + args?.candidateInfo?.Nick_Name || "Error Finding Name"} {args?.candidateInfo?.Job_Title === 'NONE' ? null : `(${args?.candidateInfo?.Job_Title})`}
                        </p>
                        <p className='m-3 p-0' style={{'margin-right': '5% !important'}}>
                        {args?.candidateInfo?.Job_Description_Title}
                        </p>
                        <p className='m-3 p-0' style={{'margin-right': '5% !important'}}>
                        JPC - {args?.candidateInfo?.Job_Code}
                        </p>
                        <p className='m-3 p-0' style={{'margin-right': '5% !important'}}>
                        {args?.candidateInfo?.date}
                        </p>
                    </div>
                    <div className='w-100 file-upload-info d-flex justify-content-center' style={{backgroundColor: 'white', height:'2%', marginBottom:'0% !important'}}> 
                        <button className="btn btn-secondary p-3" id='str' style={{color:"orange", backgroundColor:'white', borderColor:'white', marginLeft:'2% ! important', paddingTop:'0px !important', fontFamily:"Montserrat", }} onClick={handleSTRClicked}>Candidate Strengths & Skills Lacking</button>
                        <button className="btn btn-secondary p-3" id='score' style={{color:'#887e7e', backgroundColor:'white', borderColor:'white', marginLeft:'2% ! important', paddingTop:'0px !important', fontFamily:"Montserrat", }} onClick={handleCandClicked}>Candidate Score and Resume Assessment</button>
                        <button className="btn btn-secondary p-3" id='profile' style={{color:'#887e7e', backgroundColor:'white', borderColor:'white', marginLeft:'2% ! important', paddingTop:'0px !important', fontFamily:"Montserrat", }} onClick={handleProfileClicked}>Profile Comparison</button>
                        <button className="btn btn-secondary p-3" id='tech' style={{color:'#887e7e', backgroundColor:'white', borderColor:'white', marginLeft:'2% ! important', paddingTop:'0px !important', fontFamily:"Montserrat", }} onClick={handleTechClicked}>Technical Questions</button>
                        <button className="btn btn-secondary p-3" id='behavioral' style={{color:'#887e7e', backgroundColor:'white', borderColor:'white', marginLeft:'2% ! important', paddingTop:'0px !important', fontFamily:"Montserrat", }} onClick={handleBehavioralClicked}>Behavioral Questions</button>
                        {Mandatory_Questions.length > 0 && (
                        <button className="btn btn-secondary p-3" id='mandatory' style={{color:'#887e7e', backgroundColor:'white', borderColor:'white', marginLeft:'2% ! important', paddingTop:'0px !important', fontFamily:"Montserrat", }} onClick={handleMandatoryClicked}>Mandatory Questions</button>
                        )}
                    </div>
                    <div className='container' style={{width:'100%', height:'20px', backgroundColor:'#f5f5f5', "max-width":'100%'}}></div>
                { STRClicked && (
                    <>
                    <div id= 'row main' style={{display:'flex', backgroundColor:'#f5f5f5', fontSize: '14px'}}>
                    <div id='row main' style={{width:'100%', 'padding-left':'6% !important'}}>
                    <div className='file-upload-info d-flex justify-content-center' style={{color:'white', width:'97% !important', marginLeft:'3%', borderTopLeftRadius:'6px', borderTopRightRadius:'6px', backgroundColor:'#a3c87a'}}>
                        <p className='m-3 p-0' style={{fontFamily:"Montserrat"}} >
                            Candidate Strengths
                        </p>
                    </div>
                    <div className='locked-container'>
                        <TextareaAutosize
                            type="text"
                            className='newResponse interview-bubble response-text '
                            value={args?.candidateInfo?.strengths || ''}
                            style={{marginLeft:'3%'}}
                            onChange={event => handleNotesChange('strengths', 'N/A', 0, event)}
                            readOnly = {isReadOnly}
                            onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                    
                    <div className={isReadOnly? 'locked-overlay': null}>
                        {isReadOnly &&  <LockIcon style={{marginBottom:'6%'}}/>}
                    </div>
                    </div>
                    </div>
                    <div id='row main' style={{width:'100%'}}>
                    <div className='file-upload-info d-flex justify-content-center' style={{marginLeft:'3%', color:'white', width:'97%', borderTopLeftRadius:'6px', borderTopRightRadius:'6px', backgroundColor:'#F3BF42',}}>
                        <p className='m-3 p-0' style={{fontFamily:"Montserrat"}}>
                            Skills Lacking 
                        </p>
                    </div>
                     <div className='locked-container'>
                        <TextareaAutosize
                            type="text"
                            className='newResponse interview-bubble response-text  '  
                            style={{marginLeft:'5% !important', height:'60%'}}
                            value={args?.candidateInfo?.lacking_qualities || ''}
                            onChange={event => handleNotesChange('lackingQualities', 'N/A', 0, event)}
                            readOnly ={isReadOnly}
                            onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                    
                     <div className={isReadOnly? 'locked-overlay': null}>
                        {isReadOnly &&  <LockIcon style={{marginBottom:'6%'}}/>}
                    </div>
                    </div>
                    </div>


                    </div>
                     
                    </>
                    )}
                { CandidateClicked && (
                    <>
                    <div className='file-upload-info d-flex justify-content-center' style={{backgroundColor: '#9797bf', marginLeft:'6%', marginRight:'4%', color:'white', fontSize: '14px'}}>
                        <p className='m-3 p-0' style={{fontFamily:"Montserrat", fontSize: '14px'}}>
                            Candidate and Resume Assessment
                        </p>
                    </div>
                    <div id='d-flex justify-content-center' style={{backgroundColor:'#f5f5f5'}}>
                        <div className='locked-container'>
                        <TextareaAutosize
                            type="text"
                            className='newResponse interview-bubble response-text'
                            value={args?.candidateInfo?.assessment || ''}
                            style={{width:'90%', marginLeft:'6%', fontSize: '14px'}}
                            onChange={event => handleNotesChange('assessment', 'N/A', 0, event)}
                            readOnly = {isReadOnly}
                            onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                    
                     <div className={isReadOnly? 'locked-overlay': null}>
                        {isReadOnly &&  <LockIcon style={{marginBottom:'6%'}}/>}
                    </div>
                    </div>
                    </div>
                    
                    </>
                )}   
                { ProfileClicked && (
                    <> 
                    <div className='file-upload-info d-flex justify-content-center' style={{backgroundColor: '#f5cb67', marginLeft:'5%', marginRight:'5%', color:'white', fontSize: '14px'}}>
                        <p className='m-3 p-0' style={{fontFamily:"Montserrat", fontSize: '14px'}}>
                            LinkedIn Profile & Resume Comparison
                        </p>
                    </div>
                    <div id='d-flex justify-content-center'>
                     <div className='locked-container'>
                        <TextareaAutosize
                            type="text"
                            className='newResponse interview-bubble response-text'
                            value={args?.candidateInfo?.linkedCompare || ''}
                            style={{width:'90%', marginLeft:'5%', fontSize: '14px'}}
                            onChange={event => handleNotesChange('linkedCompare', 'N/A', 0, event)}
                            readOnly = {isReadOnly}
                            onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>

                        <div className={isReadOnly? 'locked-overlay': null}>
                             {isReadOnly &&  <LockIcon style={{marginBottom:'6%'}}/>}
                        </div>
                    </div>
                    </div>
                  
                    <div className='file-upload-info d-flex justify-content-center' style={{backgroundColor: '#f5cb67', marginLeft:'5%', marginRight:'5%', marginTop:'3%', color:'white', fontSize: '14px'}}>
                        <p className='m-3 p-0' style={{fontFamily:"Montserrat"}}>
                            Candidate Profile Image
                        </p>
                    </div>
                    <div id='d-flex justify-content-center'>
                    <div style={{width:'90%', marginLeft:'5%', marginBottom:'2%', backgroundColor:'white'}}>
                    <Image style={{paddingLeft:"30%" , display:"flex"}} src={args?.candidateInfo.imageSRC}  width="70%" height="60%" ></Image>
                        </div>
                    </div>
                    
                    </>
                    )}
                { TechnicalClicked && (
                    <>
                    <div className='file-upload-info d-flex justify-content-center' style={{backgroundColor: '#7F7EB0', marginLeft:'5%', marginRight:'5%', color:'white', marginBottom:'5%', fontSize: '14px'}}>
                        <p className='m-3 p-0' style={{fontFamily:"Montserrat", fontSize: '14px'}}>
                             Technical Interview Questions
                        </p>
                    </div>
                    {args?.candidateInfo?.technical_questions.map((text, num) => (
                    <>
                        <div className= 'container locked-container' style={{fontSize: '14px'}}>
                            <div className='row'>
                                <div className='col-2 m-0 blob-title' style={{color:'grey'}}>Question {num + 1} : {isReadOnly && interviewState != "Completed" && <LockIcon width="40px"/>}</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`tq.${num}`}
                                        style ={{fontSize: '14px'}}
                                        type="text"
                                        className='newResponse interview-bubble response-text'
                                        value={text.question || ''}
                                        onChange={event => handleNotesChange('technical', 'question', num, event)}
                                        readOnly = {isReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}
                                        />
                                </div>
                                <div className='col-2 m-0 blob-title' style={{color:'mediumpurple'}}>Look For: {isReadOnly && interviewState != "Completed" && <LockIcon width="40px"/>}</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`ta.${num}`}
                                        type="text"
                                        className='newResponse interview-bubble query-text'
                                        value={text.answer || ''}
                                        onChange={event => handleNotesChange('technical', 'answer', num, event)}
                                        readOnly = {isReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}
                                        />
                                </div>
                                <div className='col-2 m-0 blob-title' style={{color:'grey'}}>Notes:</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`tn.${num}`}
                                        type="text"
                                        className='newResponse interview-bubble notes-text'
                                        value={text.notes || ''}
                                        onChange={event => handleNotesChange('technical', 'notes', num, event)}
                                        readOnly = {notesReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}
                                        />
                                </div>

                                <div className='col-2 m-0 blob-title'></div>
                                <div className='col-10'>
                                    <button style={{border:'orange', backgroundColor:'#E08537', borderRadius:'6px', marginLeft:'45%'}} onClick={() => regenerateTechnical(num)}><ReloadIcon width="40px"/></button>
                                </div>
                                {num < args?.candidateInfo?.technical_questions.length -1 ? (
                                    <div className='separator mt-3 mb-5'></div>
                                ): null}
                            </div>

                            <div className={isReadOnly && interviewState === "Completed" ? 'locked-overlay': null}>
                                {isReadOnly && interviewState === "Completed" && <LockIcon style={{marginBottom:'6%'}}/>}
                            </div>
                        </div>
                    </>
                    ))}  
                    { interviewState === 'Pending' &&
                    <div className='d-flex justify-content-center' style={{marginTop:'5%'}}>
                        <button className='btn btn-success ' style={{fontFamily:"Montserrat"}} onClick={() => customQuestion('technical')}>Add Custom Technical Question</button>
                    </div>
                    }
                    </>
                )}
                { BehavioralClicked && (
                        <>
                    <div className=' file-upload-info d-flex justify-content-center ' style={{backgroundColor: '#4CABAB', marginLeft:'5%', marginRight:'5%', color:'white', marginBottom:'5%', fontSize: '14px'}}>
                        <p className='m-3 p-0'  style={{fontFamily:"Montserrat", fontSize: '14px'}}>
                             Behavioral Interview Questions
                        </p>
                    </div>
                    {args?.candidateInfo?.general_questions.map((text, num) => (
                    <>
                        <div className='container locked-container' style={{fontSize: '14px'}}>
                            <div className='row'>
                                <div className='col-2 m-0 blob-title' style={{color:'grey'}}>Question {num + 1}: {isReadOnly && interviewState != "Completed" && <LockIcon width="40px"/>}</div>
                                <div className='col-10'>
                                    <TextareaAutosize
                                        key={`gq.${num}`}
                                        type="text"
                                        className='newResponse interview-bubble response-text '
                                        value={text.question || ''}
                                        onChange={event => handleNotesChange('general', 'question', num, event)}
                                        readOnly = {isReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                                </div>
                                <div className='col-2 m-0 blob-title' style={{color:'#4CABAB'}} >Look For: {isReadOnly && interviewState != "Completed" && <LockIcon width="40px"/>}</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`ga.${num}`}
                                        type="text"
                                        className='newResponse interview-bubble'
                                        value={text.answer || ''}
                                        onChange={event => handleNotesChange('general', 'answer', num, event)}
                                        readOnly = {isReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                                </div>
                                <div className='col-2 blob-title' style={{color:'grey'}}>Notes:</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`gn.${num}`}
                                        type="text"
                                        className='newResponse interview-bubble notes-text'
                                        value={text.notes || ''}
                                        onChange={event => handleNotesChange('general', 'notes', num, event)}
                                        readOnly = {notesReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                                </div>
                                <div className='col-2 m-0 blob-title'></div>
                                <div className='col-10'>
                                    <button style={{border:'orange', backgroundColor:'#E08537', borderRadius:'6px', marginLeft:'45%'}} onClick={() => regenerateBehavioral(num)}><ReloadIcon width="40px"/></button>
                                </div>
                                {num < args?.candidateInfo?.general_questions.length -1 ? (
                                    <div className='separator mt-3 mb-5'></div>
                                ): null}

                            
                            </div>

                            <div className={isReadOnly && interviewState === "Completed" ? 'locked-overlay': null}>
                             {isReadOnly && interviewState === "Completed" && <LockIcon style={{marginBottom:'6%'}}/>}
                            </div>
                        </div>
                    </>
                    ))}
                    { ( interviewState === 'Pending' &&
                    <div className='d-flex justify-content-center' style={{marginTop:'5%'}}>
                        <button className='btn btn-success'  style={{fontFamily:"Montserrat"}} onClick={() => customQuestion('behavioral')}>Add Custom Behavioral Question</button>
                    </div>
                    )}
                    </>
                )}

                    { MandatoryClicked && (
                        <>
                    <div className=' file-upload-info d-flex justify-content-center ' style={{backgroundColor: '#4CABAB', marginLeft:'5%', marginRight:'5%', color:'white', marginBottom:'5%', fontSize: '14px'}}>
                        <p className='m-3 p-0'  style={{fontFamily:"Montserrat"}}>
                             Mandatory Interview Questions
                        </p>
                    </div>
                    {args?.candidateInfo?.Mandatory_Questions.map((text, num) => (
                    <>
                        <div className='container locked-container'>
                            <div className='row'>
                                <div className='col-2 m-0 blob-title' style={{color:'grey'}}>Question {num + 1}: {isReadOnly && interviewState != "Completed" && <LockIcon width="40px"/>}</div>
                                <div className='col-10'>
                                    <TextareaAutosize
                                        key={`gq.${num}`}
                                        style={{fontSize: '14px'}}
                                        type="text"
                                        className='newResponse interview-bubble response-text '
                                        value={text.question || ''}
                                        onChange={event => handleNotesChange('MandatoryQS', 'question', num, event)}
                                        readOnly = {isReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                                </div>
                                <div className='col-2 m-0 blob-title' style={{color:'#4CABAB'}} >Look For: {isReadOnly && interviewState != "Completed" && <LockIcon width="40px"/>}</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`ga.${num}`}
                                        type="text"
                                        style ={{fontSize: '14px'}}
                                        className='newResponse interview-bubble'
                                        value={text.lookFor || ''}
                                        onChange={event => handleNotesChange('MandatoryQS', 'lookFor', num, event)}
                                        readOnly = {isReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                                </div>
                                <div className='col-2 blob-title' style={{color:'grey'}}>Notes:</div>
                                <div className='col-10'>
                                    <TextareaAutosize 
                                        key={`gn.${num}`}
                                        type="text"
                                        style={{fontSize: '14px'}}
                                        className='newResponse interview-bubble notes-text'
                                        value={text.notes || ''}
                                        onChange={event => handleNotesChange('MandatoryQS', 'notes', num, event)}
                                        readOnly = {notesReadOnly}
                                        onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                                </div>
                                {num < args?.candidateInfo?.general_questions.length -1 ? (
                                    <div className='separator mt-3 mb-5'></div>
                                ): null}
                            </div>

                            <div className={isReadOnly  && interviewState === "Completed" ? 'locked-overlay': null}>
                                {isReadOnly && interviewState === "Completed" && <LockIcon style={{marginBottom:'6%'}}/>}
                            </div>
                        </div>
                    </>
                    ))}
                    { Mandatory_Questions.length < 4 && interviewState === 'Pending' && (
                    <div className='d-flex justify-content-center'>
                        <button className='btn btn-success'  style={{fontFamily:"Montserrat"}} onClick={() => customQuestion('mandatory')}>Add Custom Mandatory Question</button>
                    </div>
                    )}
                    </>
                )}

                 <div className='file-upload-info d-flex justify-content-center' style={{marginTop:'5%'}}>
                        <p className='m-3 p-0'  style={{fontFamily:"Montserrat", fontSize: '14px'}}>
                            Overall Notes
                        </p>
                    </div>
                    <div id='d-flex justify-content-center'>
                        <TextareaAutosize
                            type="text"
                            style ={{fontSize: '14px', borderRadius:"0px"}}
                            className='interview-bubble mx-auto response-text'
                            minRows={5}
                            value={args?.candidateInfo?.overall_notes || ''}
                            onChange={event => handleNotesChange('overall', 'N/A', 0, event)}
                            readOnly = {notesReadOnly}
                            onBlur={() => args.setCandidateInfo({...args?.candidateInfo})}/>
                    </div>

                <div className='display-button-container'>
                { interviewState !== "Completed" && 
                    <button type="button" className="btn btn-primary" data-bs-toggle="modal" data-bs-target="#approvalModal" style={{ backgroundColor:'green', border:'green'}} onClick={() => handleStateChange()}>{buttonName} </button>
                }
                { interviewState === "Completed" &&
                    <button type="button" className="btn btn-primary" style={{ backgroundColor:'orange', border:'orange'}} onClick={() => getAndDownloadInfo(String(args?.candidateInfo.uuid))}>Download</button>

                }
                
                    <button type="button" className="btn btn-primary" style={{ backgroundColor: unsavedData ? "orange": "grey", border: unsavedData ? "orange": "grey"}} onClick={() => saveCandidateData()}>Save</button>
                </div>
                </div>
            </div>
            </div>
            <ApproveModal onCloseModal = {handleApprovalClose} args={{saveData: args?.candidateInfo, isOpen: approveOpened, setIsOpen: setApproveOpened, handleSaveCandidate: args.handleSaveCandidate, setJobDescPage: args.setJobDescPage}}/>
        </>
    )
}
export default DisplayInterview;