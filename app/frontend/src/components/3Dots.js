import '../App.css';
import {React, useEffect, useState} from "react"
import {MeatBall} from "../assets/icons/meatballMenu.tsx"
import { Navbar, Container, Image, Form, FormControl, DropdownToggle, DropdownMenu, Dropdown} from 'react-bootstrap';

const ThreeDots = (args) =>{
    const [State, setState] = useState(false)
    const [JDStatus , setJDStatus] = useState(null)
    const [data, setData] = useState(args.presetData)

 
    var ChangeStatus = ''
    var btnColor

    if(State)
    {
        if(JDStatus == 'Active')
        {
            ChangeStatus = 'Inactive'
            btnColor = 'orange'
        }
    else
        {
            ChangeStatus = 'Active'
            btnColor = 'green'
       
        }
    }

    
    useEffect(() =>{
        
        console.log(args.presetData)
    
        if(args.presetData.length !=0)
        {
            setData(args.presetData)
            if(data)
                setJDStatus(data.Status)
        }

    }, [args.presetData, data, JDStatus])

return(
    <>
        <div>
            <button className='btn' style={{border:'none'}} onClick={() => setState(!State)}>
                <MeatBall/>
            </button>
            
            { State && (
                <div className='container' style={{position:'relative', zIndex:10}}>
                <div>
                    <button className='btn' id='status' style={{color:'white', backgroundColor: btnColor, marginRight:'7%'}}> Set {ChangeStatus}</button>
                    <button className='btn' style={{color:'white', backgroundColor:'red'}}> Archive </button>
              
                </div>
                </div>
            )}
        </div>
       
    </>
)

}

export default ThreeDots;