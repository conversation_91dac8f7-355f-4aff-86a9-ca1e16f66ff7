import json
import requests
import os
from dotenv import load_dotenv
from openai import OpenAI


# This file contains all llm inferencing, currently using openaiAPI, but to be swapped with private gpt with rag architecture at some point
# Also contains the call to the company's proxycurl account which gets the linkedIn data of the candidate

def openai_api_GPT4_st(prompt):
    """
    Calls the OpenAI API to generate interview questions based on the given prompt.

    Args:
        prompt (str): The prompt for generating interview questions.

    Returns:
        dict: The JSON response from the OpenAI API.
    """
    load_dotenv()
    messages = [
        {
            "role": "system", 
            "content": "You are a helpful assistant for interviewers meant to generate interview questions that will provide the interviewer confidence in assessing if the candidate has the skills required to do the job as described in the job description. The goal of the interview is to ask insightful questions that will determine whether the candidate actually has the skills that are needed for the job. The tone of your questions and your assessment of the candidate should be critical and thourough."
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]
    response = requests.post(
        "https://api.openai.com/v1/chat/completions",
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {os.environ.get('OPENAI_API_KEY')}",
        },
        json={
            "model": "gpt-4",
            "messages": messages,
            "max_tokens": 2000,
            "n": 1,
            "stop": None,
            "temperature": 0.8,
        },
    )
    return response.json()


def google_gemeni_st(prompt):
    """
    Calls the OpenAI API to generate interview questions based on the given prompt.

    Args:
        prompt (str): The prompt for generating interview questions.

    Returns:
        dict: The JSON response from the OpenAI API.
    """
    load_dotenv()
    client = OpenAI(
        api_key= os.environ.get('GEMINI_API_KEY'),
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
    )
    
    response = client.chat.completions.create(
    model="gemini-2.0-flash",
    messages = [
        {
            "role": "system", 
            "content": "You are a helpful assistant for interviewers meant to generate interview questions that will provide the interviewer confidence in assessing if the candidate has the skills required to do the job as described in the job description. The goal of the interview is to ask insightful questions that will determine whether the candidate actually has the skills that are needed for the job. The tone of your questions and your assessment of the candidate should be critical and thourough."
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]
    )
   # print(response.choices[0].message.content)
   
    return response.choices[0].message.content



def openai_api_GPT4_linkedin(prompt):
    """
    Calls the OpenAI API to generate candidate assessment using the GPT-4 model based on the given prompt.

    Args:
        prompt (str): The prompt for generating the response.

    Returns:
        dict: The JSON response from the OpenAI API.
    """
    load_dotenv()
    messages = [
        {
            "role": "system", 
            "content": "You are a helpful assistant for interviewers meant to generate an insightful assessment of candidates. A lot of candidates lie on their resume and your job is to weed them out by comparing their linked in profile data with their resume. Your assesments should be critical and put the hiring companies interests first and foremost. "
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]
    response = requests.post(
        "https://api.openai.com/v1/chat/completions",
        headers={
            "Content-Type": "application/json",
            "Authorization": f"Bearer {os.environ.get('OPENAI_API_KEY')}",
        },
        json={
            "model": "gpt-4",
            "messages": messages,
            "max_tokens": 2000,
            "n": 1,
            "stop": None,
            "temperature": 0.8,
        },
    )
    return response.json()

def google_gemeni_api_linkedIn(prompt):
    """
    Calls the OpenAI API to generate interview questions based on the given prompt.

    Args:
        prompt (str): The prompt for generating interview questions.

    Returns:
        dict: The JSON response from the OpenAI API.
    """
    load_dotenv()
    client = OpenAI(
        api_key= os.environ.get('GEMINI_API_KEY'),
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
    )
    
    response = client.chat.completions.create(
    model="gemini-2.0-flash",
    messages = [
        {
            "role": "system", 
            "content": "You are a helpful assistant for interviewers meant to generate an insightful assessment of candidates. A lot of candidates lie on their resume and your job is to weed them out by comparing their linked in profile data with their resume. Your assesments should be critical and put the hiring companies interests first and foremost."
        },
        {
            "role": "user", 
            "content": prompt
        }
    ]
    )
   # print(response.choices[0].message.content)
   
    return response.choices[0].message.content



def get_profile(profile_id):
    """
    Retrieves the profile information of a LinkedIn user.

    Args:
        profile_id (str): The LinkedIn profile ID.

    Returns:
        dict: The profile information in JSON format.
    """
    load_dotenv()
    # Proxycurl API is a service for scraping LinkedIn data
    api_endpoint = 'https://nubela.co/proxycurl/api/v2/linkedin'
    # header for the API request which includes authorization field to authenticate the request
    header_dic = {'Authorization': 'Bearer ' + os.environ.get('PROXYCURL_API_KEY')}
    # query parameters for the API request which includes the URL of the LinkedIn profile
    params = {
        'url': f'https://www.linkedin.com/in/{profile_id}',
    }
    response = requests.get(api_endpoint,
                            params=params,
                            headers=header_dic)
    return response.json()


