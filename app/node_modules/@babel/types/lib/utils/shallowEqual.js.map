{"version": 3, "names": ["shallowEqual", "actual", "expected", "keys", "Object", "key"], "sources": ["../../src/utils/shallowEqual.ts"], "sourcesContent": ["export default function shallowEqual<T extends object>(\n  actual: object,\n  expected: T,\n): actual is T {\n  const keys = Object.keys(expected) as (keyof T)[];\n\n  for (const key of keys) {\n    if (\n      // @ts-expect-error maybe we should check whether key exists first\n      actual[key] !== expected[key]\n    ) {\n      return false;\n    }\n  }\n\n  return true;\n}\n"], "mappings": ";;;;;;AAAe,SAASA,YAAYA,CAClCC,MAAc,EACdC,QAAW,EACE;EACb,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACD,QAAQ,CAAgB;EAEjD,KAAK,MAAMG,GAAG,IAAIF,IAAI,EAAE;IACtB,IAEEF,MAAM,CAACI,GAAG,CAAC,KAAKH,QAAQ,CAACG,GAAG,CAAC,EAC7B;MACA,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}